{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:8083/JobUp", "description": "Generated server url"}], "paths": {"/api/worker/create": {"post": {"tags": ["worker-controller"], "operationId": "createWorker", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Worker"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Worker"}}}}}}}, "/api/worker/search/location": {"get": {"tags": ["worker-controller"], "operationId": "searchByLocation", "parameters": [{"name": "location", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Worker"}}}}}}}}, "/api/worker/search/job": {"get": {"tags": ["worker-controller"], "operationId": "searchByJobType", "parameters": [{"name": "jobType", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Worker"}}}}}}}}, "/api/worker/getall": {"get": {"tags": ["worker-controller"], "operationId": "getAllWorkers", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Worker"}}}}}}}}, "/api/worker/get/{id}": {"get": {"tags": ["worker-controller"], "operationId": "getWorkerById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Worker"}}}}}}}, "/api/worker/delete/{id}": {"delete": {"tags": ["worker-controller"], "operationId": "deleteWorker", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"Worker": {"type": "object"}}}}