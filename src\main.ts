import { bootstrapApplication } from '@angular/platform-browser';
import { provideHttpClient } from '@angular/common/http';
import { AppComponent } from './app/app.component';
import { appConfig } from './app/app.config';

// ✅ Imports OpenAPI Generator
import { Configuration, provideApi } from './generated-sources/openapi';

// ✅ Création de la config avec le bon endpoint
const openApiConfig = new Configuration({
  basePath: 'http://localhost:8083/JobUp', // Ton backend Spring Boot
});

bootstrapApplication(AppComponent, {
  ...appConfig,
  providers: [
    provideHttpClient(),
    provideApi(openApiConfig), // ✅ injecter correctement avec config
  ],
}).catch((err) => console.error(err));
