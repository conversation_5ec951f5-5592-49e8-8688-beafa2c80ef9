import { bootstrapApplication } from '@angular/platform-browser';
import { provideHttpClient } from '@angular/common/http';
import { provideApi } from './generated-sources/openapi';

import { AppComponent } from './app/app.component';
import { appConfig } from './app/app.config';

bootstrapApplication(AppComponent, {
  ...appConfig,
  providers: [
    provideHttpClient(),
    provideApi(), // ✅ ajoute les services OpenAPI auto-générés
  ],
}).catch((err) => console.error(err));
